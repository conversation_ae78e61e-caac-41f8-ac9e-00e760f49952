{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/api/weather/route.ts"], "sourcesContent": ["export async function GET(req: Request, res: Response) {\r\n  const { searchParams } = new URL(req.url);\r\n  const city = searchParams.get(\"city\") || \"Salt Lake City\";\r\n  const apiKey = process.env.WEATHER_API_KEY;\r\n\r\n  const url = `http://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${apiKey}`;\r\n  const response = await fetch(url);\r\n\r\n  const data = await response.json();\r\n  return new Response(JSON.stringify(data));\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,eAAe,IAAI,GAAY,EAAE,GAAa;IACnD,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;IACxC,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;IACzC,MAAM,SAAS,QAAQ,GAAG,CAAC,eAAe;IAE1C,MAAM,MAAM,CAAC,iDAAiD,EAAE,KAAK,OAAO,EAAE,QAAQ;IACtF,MAAM,WAAW,MAAM,MAAM;IAE7B,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;AACrC", "debugId": null}}]}