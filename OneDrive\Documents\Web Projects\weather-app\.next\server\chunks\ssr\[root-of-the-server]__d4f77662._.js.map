{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/roboto_d549e3e.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"roboto_d549e3e-module__cPpiHq__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/roboto_d549e3e.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Roboto%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22roboto%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Roboto', 'Roboto Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/navbar/Navbar.tsx"], "sourcesContent": ["import { MagnifyingGlassIcon } from \"@heroicons/react/24/outline\";\r\n\r\nexport default function Navbar() {\r\n  return (\r\n    <nav className=\"flex flex-row items-center justify-space-between  px-4 gap-4 mt-5 w-full\">\r\n      <h1 className=\"text-xl font-bold\">Weatherly</h1>\r\n      <ul className=\"flex flex-row items-center justify-end flex-grow\">\r\n        <li>a</li>\r\n      </ul>\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAClC,8OAAC;gBAAG,WAAU;0BACZ,cAAA,8OAAC;8BAAG;;;;;;;;;;;;;;;;;AAIZ", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/ui/footer/Footer.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"footer\": \"Footer-module__32L0La__footer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/footer/Footer.tsx"], "sourcesContent": ["import styles from \"./Footer.module.css\";\r\n\r\nexport default function Footer() {\r\n  return (\r\n    <footer\r\n      className={`${styles.footer} flex flex-row items-center justify-space-between px-4 gap-4 w-full text-secondary-foreground shadow-t-4 inset-shadow-sm`}\r\n    >\r\n      <p>Weatherly</p>\r\n      <p>2025</p>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QACC,WAAW,GAAG,yIAAA,CAAA,UAAM,CAAC,MAAM,CAAC,wHAAwH,CAAC;;0BAErJ,8OAAC;0BAAE;;;;;;0BACH,8OAAC;0BAAE;;;;;;;;;;;;AAGT", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON>o } from \"next/font/google\";\nimport \"./globals.css\";\nimport Navbar from \"./ui/navbar/Navbar\";\nimport Footer from \"./ui/footer/Footer\";\n\nconst roboto = Roboto({\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Create Next App\",\n  description: \"Generated by create next app\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className={roboto.className}>\n        <Navbar />\n        <main>{children}</main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;;;;;;AAMO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;8BAC/B,8OAAC,8HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;8BAAM;;;;;;8BACP,8OAAC,8HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}