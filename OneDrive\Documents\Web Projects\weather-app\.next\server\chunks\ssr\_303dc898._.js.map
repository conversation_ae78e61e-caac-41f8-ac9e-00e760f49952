{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/WeatherForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { useWeather } from \"../context/WeatherContext\";\r\nimport { MapPinIcon, ChevronRightIcon } from \"@heroicons/react/24/outline\";\r\n\r\nconst WeatherForm = () => {\r\n  const [city, setCity] = useState(\"Salt Lake City\");\r\n  const { setWeather, setupWeatherData } = useWeather();\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    const res = await fetch(\"/api/weather?city=\" + encodeURIComponent(city));\r\n    const weatherData = await res.json();\r\n    setupWeatherData([weatherData]);\r\n    console.log([weatherData]);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <form onSubmit={handleSubmit} className=\"flex flex-row gap-4 w-full\">\r\n        <div className=\"flex flex-row gap-2 bg-foreground rounded-xl text-primary p-4 shadow-lg flex-1 w-full\">\r\n          <MapPinIcon className=\"h-6 w-6 text-primary md:h-8 md:w-8\" />\r\n          <input\r\n            type=\"text\"\r\n            name=\"city\"\r\n            placeholder=\"City...\"\r\n            value={city}\r\n            className=\"text-xl outline-none flex-grow md:text-2xl\"\r\n            onChange={(e) => setCity(e.target.value)}\r\n          />\r\n          <button type=\"submit\" className=\"text-primary\">\r\n            <ChevronRightIcon className=\"h-6 w-6 text-primary md:h-8 md:w-8\" />\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WeatherForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,cAAc;IAClB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAElD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,MAAM,MAAM,MAAM,MAAM,uBAAuB,mBAAmB;QAClE,MAAM,cAAc,MAAM,IAAI,IAAI;QAClC,iBAAiB;YAAC;SAAY;QAC9B,QAAQ,GAAG,CAAC;YAAC;SAAY;IAC3B;IAEA,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;sBACtC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,mNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,8OAAC;wBACC,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,WAAU;wBACV,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;kCAEzC,8OAAC;wBAAO,MAAK;wBAAS,WAAU;kCAC9B,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;uCAEe", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/wind_speed/WindSpeed.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useWeather } from \"../../context/WeatherContext\";\r\n\r\nconst WindSpeed = () => {\r\n  const { weather } = useWeather();\r\n  const windSpeed = weather?.windSpeed ?? 25;\r\n  const windDirection = weather?.windDirection ?? 90;\r\n  return (\r\n    <div className=\"flex flex-col bg-primary rounded-xl p-4 shadow-lg\">\r\n      <div className=\"text-lg font-bold\">Wind Speed</div>\r\n      <div className=\"text-2xl\">{windSpeed} mph</div>\r\n      <div className=\"text-md\">Direction: {windDirection}&deg;</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WindSpeed;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,YAAY;IAChB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,YAAY,SAAS,aAAa;IACxC,MAAM,gBAAgB,SAAS,iBAAiB;IAChD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAoB;;;;;;0BACnC,8OAAC;gBAAI,WAAU;;oBAAY;oBAAU;;;;;;;0BACrC,8OAAC;gBAAI,WAAU;;oBAAU;oBAAY;oBAAc;;;;;;;;;;;;;AAGzD;uCAEe", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/%40heroicons/react/24/outline/esm/MapPinIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/%40heroicons/react/24/outline/esm/ChevronRightIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}