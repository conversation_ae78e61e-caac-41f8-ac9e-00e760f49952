{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/WeatherForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { useWeather } from \"../context/WeatherContext\";\r\nimport { MapPinIcon, ChevronRightIcon } from \"@heroicons/react/24/outline\";\r\n\r\ninterface CityOption {\r\n  name: string;\r\n  country: string;\r\n  state?: string;\r\n}\r\n\r\nconst WeatherForm = () => {\r\n  const [city, setCity] = useState(\"Salt Lake City\");\r\n  const [suggestions, setSuggestions] = useState<CityOption[]>([]);\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const { setWeather } = useWeather();\r\n\r\n  useEffect(() => {\r\n    getWeather(city);\r\n  }, []);\r\n\r\n  const searchCities = async (query: string) => {\r\n    if (query.length < 2) {\r\n      setSuggestions([]);\r\n      setShowSuggestions(false);\r\n      return;\r\n    }\r\n\r\n    setIsSearching(true);\r\n    try {\r\n      // Using OpenWeatherMap's geocoding API to search for cities\r\n      const response = await fetch(\r\n        `http://api.openweathermap.org/geo/1.0/direct?q=${encodeURIComponent(\r\n          query\r\n        )}&limit=5&appid=${process.env.NEXT_PUBLIC_WEATHER_API_KEY || \"demo\"}`\r\n      );\r\n      const data = await response.json();\r\n\r\n      const cityOptions: CityOption[] = data.map((item: any) => ({\r\n        name: item.name,\r\n        country: item.country,\r\n        state: item.state,\r\n      }));\r\n\r\n      setSuggestions(cityOptions);\r\n      setShowSuggestions(cityOptions.length > 0);\r\n    } catch (error) {\r\n      console.error(\"Error searching cities:\", error);\r\n      setSuggestions([]);\r\n      setShowSuggestions(false);\r\n    } finally {\r\n      setIsSearching(false);\r\n    }\r\n  };\r\n\r\n  const debouncedSearch = (query: string) => {\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n    }\r\n\r\n    searchTimeoutRef.current = setTimeout(() => {\r\n      searchCities(query);\r\n    }, 300); // 300ms delay\r\n  };\r\n\r\n  const getWeather = async (inputCity: string) => {\r\n    const res = await fetch(\r\n      \"/api/weather?city=\" + encodeURIComponent(inputCity)\r\n    );\r\n    const weatherData = await res.json();\r\n\r\n    console.log([weatherData]);\r\n\r\n    if (weatherData.cod === \"404\") {\r\n      return;\r\n    }\r\n\r\n    const weather = {\r\n      city: weatherData.name,\r\n      temperature: weatherData.main.temp,\r\n      windSpeed: weatherData.wind.speed,\r\n      windDirection: weatherData.wind.deg,\r\n      humidity: weatherData.main.humidity,\r\n      weather: weatherData.weather.main,\r\n      maxTemp: weatherData.main.temp_max,\r\n      minTemp: weatherData.main.temp_min,\r\n      feelsLike: weatherData.main.feels_like,\r\n    };\r\n\r\n    setWeather(weather);\r\n  };\r\n\r\n  const handleCitySelect = (selectedCity: CityOption) => {\r\n    const cityName = selectedCity.state\r\n      ? `${selectedCity.name}, ${selectedCity.state}, ${selectedCity.country}`\r\n      : `${selectedCity.name}, ${selectedCity.country}`;\r\n\r\n    setCity(cityName);\r\n    setShowSuggestions(false);\r\n    setSuggestions([]);\r\n    getWeather(cityName);\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setCity(value);\r\n    debouncedSearch(value);\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setShowSuggestions(false);\r\n    await getWeather(city);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <form onSubmit={handleSubmit} className=\"flex flex-row gap-4 w-full\">\r\n        <div className=\"flex flex-row gap-2 bg-foreground rounded-xl text-primary p-4 shadow-lg flex-1 w-full\">\r\n          <MapPinIcon className=\"h-6 w-6 text-primary md:h-8 md:w-8\" />\r\n          <input\r\n            type=\"text\"\r\n            name=\"city\"\r\n            placeholder=\"City...\"\r\n            value={city}\r\n            className=\"text-xl outline-none flex-grow md:text-2xl\"\r\n            onChange={(e) => setCity(e.target.value)}\r\n          />\r\n          <button type=\"submit\" className=\"text-primary\">\r\n            <ChevronRightIcon className=\"h-6 w-6 text-primary md:h-8 md:w-8\" />\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WeatherForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAYA,MAAM,cAAc;IAClB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,eAAe,EAAE;YACjB,mBAAmB;YACnB;QACF;QAEA,eAAe;QACf,IAAI;YACF,4DAA4D;YAC5D,MAAM,WAAW,MAAM,MACrB,CAAC,+CAA+C,EAAE,mBAChD,OACA,eAAe,EAAE,QAAQ,GAAG,CAAC,2BAA2B,IAAI,QAAQ;YAExE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,cAA4B,KAAK,GAAG,CAAC,CAAC,OAAc,CAAC;oBACzD,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,OAAO;oBACrB,OAAO,KAAK,KAAK;gBACnB,CAAC;YAED,eAAe;YACf,mBAAmB,YAAY,MAAM,GAAG;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,eAAe,EAAE;YACjB,mBAAmB;QACrB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QAEA,iBAAiB,OAAO,GAAG,WAAW;YACpC,aAAa;QACf,GAAG,MAAM,cAAc;IACzB;IAEA,MAAM,aAAa,OAAO;QACxB,MAAM,MAAM,MAAM,MAChB,uBAAuB,mBAAmB;QAE5C,MAAM,cAAc,MAAM,IAAI,IAAI;QAElC,QAAQ,GAAG,CAAC;YAAC;SAAY;QAEzB,IAAI,YAAY,GAAG,KAAK,OAAO;YAC7B;QACF;QAEA,MAAM,UAAU;YACd,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,IAAI,CAAC,IAAI;YAClC,WAAW,YAAY,IAAI,CAAC,KAAK;YACjC,eAAe,YAAY,IAAI,CAAC,GAAG;YACnC,UAAU,YAAY,IAAI,CAAC,QAAQ;YACnC,SAAS,YAAY,OAAO,CAAC,IAAI;YACjC,SAAS,YAAY,IAAI,CAAC,QAAQ;YAClC,SAAS,YAAY,IAAI,CAAC,QAAQ;YAClC,WAAW,YAAY,IAAI,CAAC,UAAU;QACxC;QAEA,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,aAAa,KAAK,GAC/B,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,aAAa,KAAK,CAAC,EAAE,EAAE,aAAa,OAAO,EAAE,GACtE,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,aAAa,OAAO,EAAE;QAEnD,QAAQ;QACR,mBAAmB;QACnB,eAAe,EAAE;QACjB,WAAW;IACb;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,QAAQ;QACR,gBAAgB;IAClB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,mBAAmB;QACnB,MAAM,WAAW;IACnB;IAEA,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;sBACtC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,mNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,8OAAC;wBACC,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,WAAU;wBACV,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;kCAEzC,8OAAC;wBAAO,MAAK;wBAAS,WAAU;kCAC9B,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;uCAEe", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/wind_speed/WindMPH.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useWeather } from \"../../context/WeatherContext\";\r\n\r\nconst WindMPH = () => {\r\n  const [tempWind, setTempWind] = useState<number>(0);\r\n  const { weather } = useWeather();\r\n  const [windSpeed, setWindSpeed] = useState<number>(0);\r\n\r\n  //animation\r\n  const duration = 2000; // 2 seconds\r\n  const maxSteps = 20; // minimum number of steps for smooth animation\r\n\r\n  useEffect(() => {\r\n    setWindSpeed(Math.round(weather?.windSpeed ?? 0));\r\n  }, [weather]);\r\n\r\n  useEffect(() => {\r\n    setTempWind(0);\r\n\r\n    const steps = Math.max(maxSteps, windSpeed);\r\n    const stepDuration = duration / steps;\r\n\r\n    const timer = setInterval(() => {\r\n      setTempWind((prevTempWind) => {\r\n        if (prevTempWind >= windSpeed) {\r\n          clearInterval(timer);\r\n          return windSpeed;\r\n        }\r\n        return prevTempWind + 1;\r\n      });\r\n    }, stepDuration);\r\n\r\n    return () => {\r\n      clearInterval(timer);\r\n    };\r\n  }, [windSpeed]);\r\n\r\n  return <>{tempWind}</>;\r\n};\r\n\r\nexport default WindMPH;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,WAAW;IACX,MAAM,WAAW,MAAM,YAAY;IACnC,MAAM,WAAW,IAAI,+CAA+C;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,KAAK,KAAK,CAAC,SAAS,aAAa;IAChD,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QAEZ,MAAM,QAAQ,KAAK,GAAG,CAAC,UAAU;QACjC,MAAM,eAAe,WAAW;QAEhC,MAAM,QAAQ,YAAY;YACxB,YAAY,CAAC;gBACX,IAAI,gBAAgB,WAAW;oBAC7B,cAAc;oBACd,OAAO;gBACT;gBACA,OAAO,eAAe;YACxB;QACF,GAAG;QAEH,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC;KAAU;IAEd,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/%40heroicons/react/24/outline/esm/MapPinIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/%40heroicons/react/24/outline/esm/ChevronRightIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}