{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/WeatherForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/ui/WeatherForm.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/ui/WeatherForm.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/WeatherForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/ui/WeatherForm.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/ui/WeatherForm.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsQ,GACnS,oCACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/ui/landing-block/LandingBlock.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"landing_block_content\": \"LandingBlock-module__y37zkq__landing_block_content\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/landing-block/LandingBlock.tsx"], "sourcesContent": ["import WeatherForm from \"../WeatherForm\";\r\nimport styles from \"./LandingBlock.module.css\";\r\n\r\nexport default function LandingBlock() {\r\n  return (\r\n    <section className={`flex flex-col`}>\r\n      <div></div>\r\n      <div className={`${styles.landing_block_content} flex flex-col p-4`}>\r\n        <WeatherForm />\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAW,CAAC,aAAa,CAAC;;0BACjC,8OAAC;;;;;0BACD,8OAAC;gBAAI,WAAW,GAAG,yJAAA,CAAA,UAAM,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;0BACjE,cAAA,8OAAC,yHAAA,CAAA,UAAW;;;;;;;;;;;;;;;;AAIpB", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/wind_speed/WindSpeed.tsx"], "sourcesContent": ["import { useWeather } from \"../../context/WeatherContext\";\r\n\r\nconst WindSpeed = () => {\r\n  const { weather } = useWeather();\r\n  const windSpeed = weather?.windSpeed ?? 25;\r\n  const windDirection = weather?.windDirection ?? 90;\r\n  return (\r\n    <div className=\"flex flex-col bg-primary rounded-xl p-4 shadow-lg\">\r\n      <div className=\"text-lg font-bold\">Wind Speed</div>\r\n      <div className=\"text-2xl\">{windSpeed} mph</div>\r\n      <div className=\"text-md\">Direction: {windDirection}&deg;</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WindSpeed;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,YAAY;IAChB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,YAAY,SAAS,aAAa;IACxC,MAAM,gBAAgB,SAAS,iBAAiB;IAChD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAoB;;;;;;0BACnC,8OAAC;gBAAI,WAAU;;oBAAY;oBAAU;;;;;;;0BACrC,8OAAC;gBAAI,WAAU;;oBAAU;oBAAY;oBAAc;;;;;;;;;;;;;AAGzD;uCAEe", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/ui/content-body/ContentBody.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"content_body\": \"ContentBody-module__4pST5W__content_body\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/content-body/ContentBody.tsx"], "sourcesContent": ["import WindSpeed from \"../wind_speed/WindSpeed\";\r\nimport styles from \"./ContentBody.module.css\";\r\n\r\nexport default function ContentBody() {\r\n  return (\r\n    <section className={`${styles.content_body} flex flex-col flex-1`}>\r\n      <div className={`${styles.content_body} flex flex-col p-4 pb-20`}>\r\n        <h2>Weather Form</h2>\r\n        <WindSpeed />\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAW,GAAG,uJAAA,CAAA,UAAM,CAAC,YAAY,CAAC,qBAAqB,CAAC;kBAC/D,cAAA,8OAAC;YAAI,WAAW,GAAG,uJAAA,CAAA,UAAM,CAAC,YAAY,CAAC,wBAAwB,CAAC;;8BAC9D,8OAAC;8BAAG;;;;;;8BACJ,8OAAC,qIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\n\nimport LandingBlock from \"./ui/landing-block/LandingBlock\";\nimport ContentBody from \"./ui/content-body/ContentBody\";\n\nexport default function Home() {\n  return (\n    <main>\n      <LandingBlock />\n      <ContentBody />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,8IAAA,CAAA,UAAY;;;;;0BACb,8OAAC,4IAAA,CAAA,UAAW;;;;;;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,+RAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,+RAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,+RAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}