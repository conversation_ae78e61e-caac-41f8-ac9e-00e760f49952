{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/landing-block/LandingBlock.module.css"], "sourcesContent": ["div.landing_block_content {\r\n  background: linear-gradient(178deg, var(--primary) 1%, var(--secondary) 100%);\r\n  height: 50vh;\r\n  max-width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/content-body/ContentBody.module.css"], "sourcesContent": ["section.content_body {\r\n  background: var(--secondary);\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\ndiv.content_body {\r\n  border-top-left-radius: 1.5rem;\r\n  border-top-right-radius: 1.5rem;\r\n  background: var(--tertiary);\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;AAQA", "debugId": null}}]}