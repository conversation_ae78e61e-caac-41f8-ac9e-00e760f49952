export async function GET(req: Request, res: Response) {
  const { searchParams } = new URL(req.url);
  const query = searchParams.get("query") || "";
  const apiKey = process.env.NEXT_PUBLIC_WEATHER_API_KEY;

  const url = `https://api.openweathermap.org/geo/1.0/direct?q=${encodeURIComponent(
    query
  )}&limit=5&appid=${apiKey}`;
  const response = await fetch(url);

  const data = await response.json();
  return new Response(JSON.stringify(data));
}
