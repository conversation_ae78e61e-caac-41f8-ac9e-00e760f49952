export async function GET(req: Request, res: Response) {
  const { searchParams } = new URL(req.url);
  const city = searchParams.get("city") || "Salt Lake City";
  const apiKey = process.env.WEATHER_API_KEY;

  const url = `http://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${apiKey}`;
  const response = await fetch(url);

  const data = await response.json();
  return new Response(JSON.stringify(data));
}
