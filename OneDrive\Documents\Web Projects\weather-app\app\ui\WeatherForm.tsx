"use client";

import React, { useState } from "react";
import { useWeather } from "../context/WeatherContext";
import { MapPinIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

const WeatherForm = () => {
  const [city, setCity] = useState("Salt Lake City");
  const { setWeather } = useWeather();

  const getWeather = async (inputCity: string) => {
    const res = await fetch(
      "/api/weather?city=" + encodeURIComponent(inputCity)
    );
    const weatherData = await res.json();

    const weather = {
      city: weatherData.name,
      temperature: weatherData.main.temp,
      maxTemp: weatherData.main.temp_max,
      minTemp: weatherData.main.temp_min,
      feelsLike: weatherData.main.feels_like,
      humidity: weatherData.main.humidity,
      windSpeed: weatherData.wind.speed,
      windDirection: weatherData.wind.deg,
      weather: weatherData.weather.main,
    };

    setWeather(weather);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await getWeather(city);
  };

  return (
    <div>
      <form onSubmit={handleSubmit} className="flex flex-row gap-4 w-full">
        <div className="flex flex-row gap-2 bg-foreground rounded-xl text-primary p-4 shadow-lg flex-1 w-full">
          <MapPinIcon className="h-6 w-6 text-primary md:h-8 md:w-8" />
          <input
            type="text"
            name="city"
            placeholder="City..."
            value={city}
            className="text-xl outline-none flex-grow md:text-2xl"
            onChange={(e) => setCity(e.target.value)}
          />
          <button type="submit" className="text-primary">
            <ChevronRightIcon className="h-6 w-6 text-primary md:h-8 md:w-8" />
          </button>
        </div>
      </form>
    </div>
  );
};

export default WeatherForm;
