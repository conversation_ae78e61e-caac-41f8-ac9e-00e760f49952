"use client";

import { useWeather } from "../../context/WeatherContext";

const WindSpeed = () => {
  const { weather } = useWeather();
  const windSpeed = weather?.windSpeed ?? 25;
  const windDirection = weather?.windDirection ?? 90;
  return (
    <div className="flex flex-col bg-primary rounded-xl p-4 shadow-lg">
      <div className="text-lg font-bold">Wind Speed</div>
      <div className="text-2xl">{windSpeed} mph</div>
      <div className="text-md">Direction: {windDirection}&deg;</div>
    </div>
  );
};

export default WindSpeed;
